use crate::*;
use frame_support::{traits::Get, traits::OnRuntimeUpgrade, weights::Weight};
use log::{debug, info};
use sp_std::{marker::PhantomData, vec::Vec};

#[cfg(feature = "try-runtime")]
use frame_support::ensure;
#[cfg(feature = "try-runtime")]
use parity_scale_codec::{Decode, Encode};
#[cfg(feature = "try-runtime")]
use sp_runtime::DispatchError;

pub struct AddMyceliumPkToTwins<T: Config>(PhantomData<T>);

impl<T: Config> OnRuntimeUpgrade for AddMyceliumPkToTwins<T> {
    #[cfg(feature = "try-runtime")]
    fn pre_upgrade() -> Result<Vec<u8>, sp_runtime::TryRuntimeError> {
        info!("current pallet version: {:?}", PalletVersion::<T>::get());
        ensure!(
            PalletVersion::<T>::get() >= types::StorageVersion::V17Struct,
            DispatchError::Other("Unexpected pallet version")
        );

        let twins_count: u64 = Twins::<T>::iter().count() as u64;
        info!(
            "🔎 AddMyceliumPkToTwins pre migration: Number of existing twins {:?}",
            twins_count
        );

        info!("👥  TFGrid pallet to V18 passes PRE migrate checks ✅",);
        Ok(twins_count.encode())
    }

    fn on_runtime_upgrade() -> Weight {
        if PalletVersion::<T>::get() == types::StorageVersion::V17Struct {
            migrate_twins_add_mycelium_pk::<T>()
        } else {
            info!(" >>> Unused TFGrid pallet V18 migration");
            Weight::zero()
        }
    }

    #[cfg(feature = "try-runtime")]
    fn post_upgrade(pre_twins_count: Vec<u8>) -> Result<(), sp_runtime::TryRuntimeError> {
        info!("current pallet version: {:?}", PalletVersion::<T>::get());
        ensure!(
            PalletVersion::<T>::get() >= types::StorageVersion::V18Struct,
            DispatchError::Other("Unexpected pallet version")
        );

        // Check number of twins against pre-check result
        let pre_twins_count: u64 = Decode::decode(&mut pre_twins_count.as_slice())
            .expect("the state parameter should be something that was generated by pre_upgrade");
        ensure!(
            Twins::<T>::iter().count() as u64 == pre_twins_count,
            DispatchError::Other("Number of twins migrated does not match")
        );

        info!(
            "👥  TFGrid pallet migration to {:?} passes POST migrate checks ✅",
            Pallet::<T>::pallet_version()
        );

        Ok(())
    }
}

pub fn migrate_twins_add_mycelium_pk<T: Config>() -> frame_support::weights::Weight {
    info!(" >>> Migrating twins storage to add mycelium_pk field...");

    let mut read_writes = 0;

    // Define the old Twin struct without mycelium_pk field
    #[derive(Clone, parity_scale_codec::Encode, parity_scale_codec::Decode, Debug, Eq, PartialEq, Default)]
    pub struct OldTwin<AccountId> {
        pub id: u32,
        pub account_id: AccountId,
        pub relay: Option<frame_support::BoundedVec<u8, frame_support::traits::ConstU32<{ types::MAX_RELAY_LENGTH }>>>,
        pub entities: Vec<types::EntityProof>,
        pub pk: Option<frame_support::BoundedVec<u8, frame_support::traits::ConstU32<{ types::MAX_PK_LENGTH }>>>,
    }

    Twins::<T>::translate::<OldTwin<AccountIdOf<T>>, _>(|k, old_twin| {
        debug!("migrated twin: {:?}", k);

        let new_twin = types::Twin {
            id: old_twin.id,
            account_id: old_twin.account_id,
            relay: old_twin.relay,
            entities: old_twin.entities,
            pk: old_twin.pk,
            mycelium_pk: None, // Initialize with None for existing twins
        };

        read_writes += 1;
        Some(new_twin)
    });

    // Update pallet storage version
    PalletVersion::<T>::set(types::StorageVersion::V18Struct);
    info!(" <<< Twin migration success, storage version upgraded");

    // Return the weight consumed by the migration.
    T::DbWeight::get().reads_writes(read_writes, read_writes + 1)
}
